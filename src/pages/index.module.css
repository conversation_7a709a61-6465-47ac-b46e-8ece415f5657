/**
 * CSS files with the .module.css suffix will be treated as CSS modules
 * and scoped locally.
 */

.heroBanner {
  padding: 6rem 0;
  background: linear-gradient(135deg, rgb(24, 24, 24) 0%, rgb(45, 45, 45) 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.heroContent {
  display: flex;
  align-items: center;
  gap: 4rem;
}

.heroText {
  flex: 1;
}

.heroTitle {
  font-size: 4.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  color: #ffffff;
  letter-spacing: -0.02em;
}

.heroSubtitle {
  font-size: 1.75rem;
  margin-bottom: 3rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.heroCTA {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;
}

.heroMetrics {
  display: flex;
  gap: 2rem;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
}

.heroMetrics span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.heroVisual {
  flex: 1;
  position: relative;
}

.heroImage {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.features {
  padding: 8rem 0;
  background: #ffffff;
}

.sectionHeader {
  text-align: center;
  margin-bottom: 5rem;
}

.sectionHeader h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #000;
}

.sectionHeader p {
  font-size: 1.5rem;
  color: #666;
  max-width: 800px;
  margin: 0 auto;
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 0 1rem;
}

.featureCard {
  background: #fff;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-5px);
}

.featureIcon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.featureCard h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.featureCard p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.5;
}

.howItWorks {
  padding: 4rem 0;
}

.howItWorksTitle {
  font-size: 3.0rem;
  font-weight: bold;
  margin-bottom: 1rem;
  line-height: 1.2;
  color:rgb(0, 0, 0);
}

.howItWorksSubtitle {
  font-size: 2.0rem;
  margin-bottom: 2rem;
  color: rgb(0, 0, 0);
}

.howItWorksText {
  font-size: 1.5rem;
  margin-bottom: 4rem;
  color: rgb(0, 0, 0);
}

.howItWorks img {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.workflowSteps img {
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.workflowSteps img:hover {
  transform: translateY(-5px);
}

.workflowDot[data-step="1"] {
  background: #C73939;
}

/* Integrations Section */
.integrations {
  padding: 8rem 0;
  background: #fff;
}

.integrationGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.integrationCard {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.integrationCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.integrationLogo {
  width: 120px;
  height: 120px;
  margin: 0 auto 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.integrationLogo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.integrationCard:hover .integrationLogo {
  transform: scale(1.1);
}

.integrationCard h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.integrationCard p {
  font-size: 1rem;
  color: #666;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.integrationFeatures {
  text-align: left;
  padding: 0 1rem;
  margin-bottom: 1.5rem;
}

.integrationFeatures p {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.integrationFeatures ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.integrationFeatures li {
  font-size: 0.95rem;
  color: #666;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.featureAvailable {
  color: #28a745;
  font-weight: bold;
}

.featureComingSoon {
  color: #C73939;
  font-size: 0.9rem;
  font-weight: 500;
}



/* Capabilities Section */
.capabilities {
  padding: 8rem 0;
  background: #f8f9fa;
}

.capabilityCard {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  height: 100%;
  transition: transform 0.3s ease;
}

.capabilityCard:hover {
  transform: translateY(-5px);
}

.capabilityCard img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.capabilityContent {
  padding: 2rem;
}

.capabilityContent h3 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #000;
}

.capabilityContent p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.capabilityContent ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.capabilityContent li {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
}

.capabilityContent li:before {
  content: "✓";
  color: #C73939;
  position: absolute;
  left: 0;
  font-weight: bold;
}

.statusAvailable {
  background: #28a745;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
}

.statusSoon {
  background: #ffc107;
  color: #000;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
}

.statusPlanned {
  background: #6c757d;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
}

.getStarted {
  padding: 4rem 0;
  background-color: #e0f7fa;
  text-align: center;
}

.logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  flex-wrap: wrap;
  margin: 2rem auto;
  max-width: 1200px;
  padding: 0 1rem;
}

.logos img {
  filter: grayscale(100%);
  opacity: 0.7;
  transition: all 0.3s ease;
}

.logos img:hover {
  filter: grayscale(0%);
  opacity: 1;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  position: relative;
  max-width: 90%;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modalContent img {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
}

.modalClose {
  position: absolute;
  top: -15px;
  right: -15px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #C73939;
  color: white;
  border: none;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.modalClose:hover {
  background: #a82e2e;
}

/* Responsive Design */
@media screen and (max-width: 996px) {
  .heroContent {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .heroVisual {
    display: none;
  }

  .heroTitle {
    font-size: 2.5rem;
  }

  .heroSubtitle {
    font-size: 1.25rem;
  }

  .heroCTA {
    justify-content: center;
    flex-wrap: wrap;
  }

  .heroMetrics {
    justify-content: center;
    flex-wrap: wrap;
  }

  .sectionHeader h2 {
    font-size: 2rem;
  }

  .sectionHeader p {
    font-size: 1.25rem;
  }

  .howItWorksSubtitle {
    font-size: 1.5rem;
  }

  .howItWorksText {
    font-size: 1.25rem;
  }

  .capabilityContent h3 {
    font-size: 1.5rem;
  }

  .capabilityContent p,
  .capabilityContent li {
    font-size: 1rem;
  }

  .integrationCard {
    padding: 1.5rem;
  }

  .integrationLogo {
    width: 80px;
    height: 80px;
  }
}

@media screen and (max-width: 576px) {
  .heroBanner {
    padding: 3rem 0;
  }

  .heroTitle {
    font-size: 2rem;
  }

  .heroSubtitle {
    font-size: 1.1rem;
  }

  .heroCTA {
    flex-direction: column;
    gap: 1rem;
  }

  .heroCTA a {
    width: 100%;
    margin-left: 0 !important;
  }

  .heroMetrics {
    flex-direction: column;
    gap: 1rem;
  }

  .featureGrid {
    grid-template-columns: 1fr;
  }

  .integrationGrid {
    grid-template-columns: 1fr;
  }

  .capabilities .row > div {
    margin-bottom: 2rem;
  }
}