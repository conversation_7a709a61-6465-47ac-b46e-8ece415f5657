name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main

# Add permissions for GitHub token
permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  deploy:
    name: Deploy to GitHub Pages
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: yarn

      - name: Clear yarn cache
        run: yarn cache clean

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Clear Docusaurus cache
        run: yarn clear

      - name: Build website
        run: yarn build
        env:
          GITHUB_PAGES: true
          NODE_OPTIONS: "--max_old_space_size=4096"

      # Popular action to deploy to GitHub Pages:
      # Docs: https://github.com/peaceiris/actions-gh-pages#%EF%B8%8F-docusaurus
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          # Use the built-in GitHub token
          github_token: ${{ secrets.GITHUB_TOKEN }}
          # Build output to publish to the `gh-pages` branch:
          publish_dir: ./build
          # The following lines assign commit authorship to the official
          # GH-Actions bot for deploys to `gh-pages` branch:
          # https://github.com/actions/checkout/issues/13#issuecomment-724415212
          # You can swap them out with your own user credentials.
          user_name: "GitHub Actions"
          user_email: "<EMAIL>"
