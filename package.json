{"name": "agentq", "version": "1.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "^3.8.1", "@docusaurus/plugin-google-gtag": "^3.8.1", "@docusaurus/preset-classic": "^3.8.1", "@fortawesome/fontawesome-free": "^6.7.2", "@svgr/webpack": "^5.5.0", "clsx": "^1.1.1", "file-loader": "^6.2.0", "prism-react-renderer": "^1.2.1", "react": "^18.3.1", "react-dom": "^18.3.1", "url-loader": "^4.1.1"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@mdx-js/react": "^3.0.0"}}