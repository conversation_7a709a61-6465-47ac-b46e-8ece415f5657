---
sidebar_position: 2
---

# Release Notes


## Version 1.8

#### Improvement
- multiple source (file, public url and private url)
<img src="/agentq/img/release/v1.8/1.1.png" alt="multiple source" style={{ maxWidth: '45%'}} />
<img src="/agentq/img/release/v1.8/1.2.png" alt="multiple source" style={{ maxWidth: '45%'}} />
<br/><br/>
- provide new button to "generate more test cases" on test case review step
<img src="/agentq/img/release/v1.8/2.png" alt="search on test run detail page" style={{ maxWidth: '50%'}} />
<br/><br/>
- create defect ticket to JIRA
<img src="/agentq/img/release/v1.8/3.1.png" alt="search on test run detail page" style={{ maxWidth: '45%'}} />
<img src="/agentq/img/release/v1.8/3.2.png" alt="search on test run detail page" style={{ maxWidth: '45%'}} />
<br/><br/>
<img src="/agentq/img/release/v1.8/3.3.png" alt="search on test run detail page" style={{ maxWidth: '50%'}} />
<br/><br/>
<img src="/agentq/img/release/v1.8/3.4.png" alt="search on test run detail page" style={{ maxWidth: '50%'}} />
<br/><br/>
- add “Issues Page” for bug tracking
<img src="/agentq/img/release/v1.8/4.1.png" alt="search on test run detail page" style={{ maxWidth: '20%'}} />
<br/><br/>
<img src="/agentq/img/release/v1.8/4.2.png" alt="search on test run detail page" style={{ maxWidth: '100%'}} />


## Version 1.7.1

#### Bug Fix
- bug fix due to user cannot edit test case


## Version 1.7

#### Improvement
- add precondition test case which is generated by AI
- enable dynamic filter by test case type on test run
<img src="/agentq/img/release/v1.7/1.png" alt="search on test run detail page" style={{ maxWidth: '50%'}} />
<br/><br/>
- enable import csv file for folder (include folder child with hirarcy like folder/subfolder) and tags (able to import multiple tags such as “tag1,tag2,tag3” *should use double quoute for multiple tags
<img src="/agentq/img/release/v1.7/2.png" alt="search on test run detail page" style={{ maxWidth: '100%'}} />
<br/><br/>
- enable export csv file include folder and tags


## Version 1.6

#### Improvement
- add move test case to some folder by selection
<img src="/agentq/img/release/v1.6/1.1.png" alt="search on test run detail page" style={{ maxWidth: '50%'}} />
<br/><br/>
<img src="/agentq/img/release/v1.6/1.2.png" alt="search on test run detail page" style={{ maxWidth: '100%'}} />
<br/><br/>
- add copy test case by selection
<img src="/agentq/img/release/v1.6/2.png" alt="search on test run detail page" style={{ maxWidth: '50%'}} />


## Version 1.5

#### Improvement
- add search feature on test run detail page
<img src="/agentq/img/release/v1.5/1.png" alt="search on test run detail page" style={{ maxWidth: '30%'}} />
- add tags test cases table
<img src="/agentq/img/release/v1.5/2.png" alt="tags" style={{ maxWidth: '30%'}} />
- click detail test case will become edit test case modal

#### Bug Fix
- remove id on import csv template


## Version 1.4

#### Improvement
- add search box paggination on test cases page
<img src="/agentq/img/release/v1.4/1.png" alt="search page" style={{ maxWidth: '60%'}} />

#### Bug Fix
- solve paggination issue on test cases page, when user after edit on page > 1, will redirect to 1st page
- enable paggination on test cases page
- frontend need handle error during import test case with extend timeout
- only enable delete selected on test cases page


## Version 1.3

#### Improvement
- improve test run creation based on tags
<img src="/agentq/img/release/v1.3/1.png" alt="test run based on tags" style={{ maxWidth: '60%'}} />


## Version 1.2

#### Improvement
- remove project header on test run detail page
- enable sub folder for dynamic filter test run
- enable CSV file template for import test cases
<img src="/agentq/img/release/v1.2/1.png" alt="csv template.id" style={{ maxWidth: '50%'}} />
- enable tags on test cases creation
<img src="/agentq/img/release/v1.2/2.png" alt="tags filed.id" style={{ maxWidth: '50%'}} />

#### Bug Fix
- fix data import error with capital letters
- fix timout during import csv file on frontend
- fix test result empty after edit dynamic filter test run


## Version 1.1

#### Improvement
- improve confirmation modal with progress bar
<img src="/agentq/img/release/v1.1/1.png" alt="login agentq.id" style={{ maxWidth: '30%'}} />

- improve import modal with progress bar
<img src="/agentq/img/release/v1.1/2.png" alt="login agentq.id" style={{ maxWidth: '30%'}} />

#### Bug Fix
- bug fix pagination on test cases page
- bug fix bulk delete
